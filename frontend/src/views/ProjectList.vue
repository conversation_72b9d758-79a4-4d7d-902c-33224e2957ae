<template>
  <!-- 🌸 浪漫风格项目管理页面 -->
  <div class="dashboard-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-text">
          <h1 class="page-title">项目管理</h1>
          <p class="page-description">管理您的所有项目和部署配置 ✨</p>
        </div>
        <div class="header-actions">
          <button class="btn-add" @click="showCreateDialog">
            <Icon name="plus" :size="20" />
            新建项目
          </button>
        </div>
      </div>
    </div>


    <!-- 快捷筛选按钮 -->
    <div class="mb-6">
      <div class="flex flex-wrap gap-3">
        <button
          class="btn btn-sm"
          :class="activeFilter === 'all' ? 'btn-romantic' : 'btn-outline'"
          @click="setFilter('all')"
        >
          <Icon name="sparkles" :size="16" />
          全部项目
        </button>
        <button
          class="btn btn-sm"
          :class="activeFilter === 'robobus' ? 'btn-romantic' : 'btn-outline'"
          @click="setFilter('robobus')"
        >
          <Icon name="smartphone" :size="16" />
          小程序
        </button>
        <button
          class="btn btn-sm"
          :class="activeFilter === 'ccapi' ? 'btn-romantic' : 'btn-outline'"
          @click="setFilter('ccapi')"
        >
          <Icon name="monitor" :size="16" />
          运营平台
        </button>
        <button
          class="btn btn-sm"
          :class="activeFilter === 'ccserver' ? 'btn-romantic' : 'btn-outline'"
          @click="setFilter('ccserver')"
        >
          <Icon name="server" :size="16" />
          TCP服务器
        </button>
        <div class="divider divider-vertical mx-2 hidden sm:block"></div>
        <button
          class="btn btn-sm transition-all duration-200"
          :class="activeFilter === 'prod' ? 'btn-error text-white border-red-500 bg-red-500 shadow-lg' : 'btn-outline border-red-300 text-red-600 hover:bg-red-50'"
          @click="setFilter('prod')"
        >
          <Icon name="alert-triangle" :size="16" />
          生产环境
        </button>
        <button
          class="btn btn-sm transition-all duration-200"
          :class="activeFilter === 'test' ? 'btn-info text-white border-blue-500 bg-blue-500 shadow-lg' : 'btn-outline border-blue-300 text-blue-600 hover:bg-blue-50'"
          @click="setFilter('test')"
        >
          <Icon name="beaker" :size="16" />
          测试环境
        </button>
      </div>
    </div>

    <!-- 项目卡片网格 -->
    <div class="mb-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <h2 class="text-xl font-semibold text-gray-800 flex-shrink-0">项目列表</h2>
        <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4">
          <!-- 视图切换按钮 -->
          <div class="btn-group flex-shrink-0">
            <button
              class="btn btn-sm flex-1 sm:flex-initial"
              :class="viewMode === 'grid' ? 'btn-romantic' : 'btn-ghost'"
              @click="viewMode = 'grid'"
            >
              <Icon name="grid" :size="16" />
              <span class="hidden sm:inline ml-1">网格</span>
            </button>
            <button
              class="btn btn-sm flex-1 sm:flex-initial"
              :class="viewMode === 'list' ? 'btn-romantic' : 'btn-ghost'"
              @click="viewMode = 'list'"
            >
              <Icon name="list" :size="16" />
              <span class="hidden sm:inline ml-1">列表</span>
            </button>
          </div>

          <!-- 排序选择 -->
          <select class="select select-bordered select-sm w-full sm:w-32 flex-shrink-0" v-model="sortBy">
            <option value="name">按名称</option>
            <option value="environment">按环境</option>
            <option value="created">按创建时间</option>
            <option value="updated">按更新时间</option>
          </select>
        </div>
      </div>

      <!-- 卡片网格视图 -->
      <div v-if="viewMode === 'grid'" class="w-full max-w-full">
        <!-- 项目卡片 -->
        <div v-if="!isLoading && projects.length > 0" class="dashboard-grid project-grid-animated">
          <ProjectCard
            v-for="(project, index) in sortedProjects"
            :key="project.id"
            :project="project"
            :is-loading="loadingProjects.includes(project.id)"
            :variant="getCardVariant(project)"
            @click="editProject"
            @edit="editProject"
            @deploy="oneClickDeploy"
            @build="buildProject"
            @artifact="showArtifactDialog"
            @logs="viewLogs"
            @delete="deleteProject"
          />
        </div>
      </div>

      <!-- 列表视图（保留原表格） -->
      <div v-else class="w-full max-w-full overflow-hidden">
        <!-- 实际项目表格 -->
        <div v-if="!isLoading && projects.length > 0" class="table-fade-in w-full">
          <project-table
            :projects="sortedProjects"
            :pagination="pagination"
            :deploy-logs="deployLogs"
            :log-panel-visible="logDialogVisible"
            @edit="editProject"
            @deploy="oneClickDeploy"
            @build="buildProject"
            @artifact="showArtifactDialog"
            @logs="viewLogs"
            @delete="deleteProject"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            @close-log-panel="closeLogPanel"
          />
        </div>
      </div>
    </div>

    <!-- 分页组件 -->
    <Pagination
      :current-page="pagination.currentPage"
      :page-size="pagination.pageSize"
      :total="pagination.total"
      @page-change="handleCurrentChange"
    />
    <!-- 新建/编辑项目弹窗 -->
    <project-dialog
      v-model:visible="createDialogVisible"
      :project="newProject"
      :is-edit="false"
      @save="createProject"
      @cancel="createDialogVisible = false"
    />
    <project-dialog
      v-model:visible="editDialogVisible"
      :project="editingProject"
      :is-edit="true"
      @save="updateProject"
      @cancel="editDialogVisible = false"
    />
    <!-- 日志面板 -->
    <log-panel
      v-model:visible="logDialogVisible"
      :logs="deployLogs"
      @clear="clearLogs"
      @refresh="refreshLogs"
      @download="downloadLogs"
    />
    <!-- 历史构建产物弹窗 -->
    <artifact-dialog
      v-model:visible="artifactDialogVisible"
      :artifacts="artifactList"
      :project-id="currentProjectId"
      @download="downloadArtifact"
      @star="toggleStar"
      @deploy="deployArtifact"
      @delete="deleteArtifact"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import Icon from '../components/Icon.vue'
import ProjectCard from '../components/ProjectCard.vue'
import ProjectTable from '../components/ProjectTable.vue'
import ProjectDialog from '../components/ProjectDialog.vue'
import LogPanel from '../components/LogPanel.vue'
import ArtifactDialog from '../components/ArtifactDialog.vue'
import Pagination from '../components/common/Pagination.vue'

import { toast } from '../composables/useToast.js'
import { confirm } from '../composables/useConfirm.js'

// 🌸 使用全局Toast通知系统
const showMessage = (message, type = 'info') => {
  switch(type) {
    case 'success':
      toast.success(message)
      break
    case 'error':
      toast.error(message)
      break
    case 'warning':
      toast.warning(message)
      break
    case 'info':
    default:
      toast.info(message)
      break
  }
}
import {
  getProjects,
  createProject as apiCreateProject,
  updateProject as apiUpdateProject,
  deleteProject as apiDeleteProject,
  buildProject as apiBuildProject,
  buildAndDeployProject as apiBuildAndDeployProject
} from '../api/project'
import { getArtifactList, deployArtifact as apiDeployArtifact, deleteArtifact as apiDeleteArtifact, toggleArtifactStar } from '../api/artifact'

const projects = ref([])
const pagination = ref({ currentPage: 1, pageSize: 12, total: 0 })
const createDialogVisible = ref(false)
const editDialogVisible = ref(false)
const logDialogVisible = ref(false)
const artifactDialogVisible = ref(false)
const deployLogs = ref([])
const artifactList = ref([])
const currentProjectId = ref(null)
const loadingProjects = ref([])
const viewMode = ref('grid') // 'grid' 或 'list'
const sortBy = ref('environment') // 'name', 'environment', 'created', 'updated'
const activeFilter = ref('all') // 当前激活的筛选器
const currentFilters = ref({}) // 当前的筛选条件
const isLoading = ref(false) // 初始状态不显示加载
let ws = null

// 计算属性：排序后的项目列表
const sortedProjects = computed(() => {
  const sorted = [...projects.value]

  switch (sortBy.value) {
    case 'name':
      return sorted.sort((a, b) => a.name.localeCompare(b.name))
    case 'environment':
      return sorted.sort((a, b) => {
        // 生产环境排在前面
        if (a.environment === 'prod' && b.environment !== 'prod') return -1
        if (a.environment !== 'prod' && b.environment === 'prod') return 1
        if (a.environment === 'test' && b.environment !== 'test') return -1
        if (a.environment !== 'test' && b.environment === 'test') return 1
        return a.name.localeCompare(b.name) // 同环境按名称排序
      })
    case 'created':
      return sorted.sort((a, b) => new Date(b.created_at || 0) - new Date(a.created_at || 0))
    case 'updated':
      return sorted.sort((a, b) => new Date(b.updated_at || 0) - new Date(a.updated_at || 0))
    default:
      return sorted
  }
})

// 获取卡片变体 - 基于环境的逻辑颜色区分
const getCardVariant = (project) => {
  // 基于环境分配颜色：生产环境用红色系，测试环境用蓝色系
  if (project.environment === 'prod') {
    return 'production'  // 生产环境 - 红色系，表示重要和谨慎
  } else if (project.environment === 'test') {
    return 'testing'     // 测试环境 - 蓝色系，表示安全和开发
  } else {
    return 'default'     // 默认 - 灰色系
  }
}

const initialProjectForm = {
  name: '',
  description: '',
  git_url: '',
  git_username: '',
  git_password: '',
  git_version: '',
  deploy_type: 'local',
  environment: 'test',
  project_type: 'frontend',
  build_config: {
    language: 'go',
    config: {
      server_os: 'linux',
      server_arch: 'amd64',
      port: '8080',
      node_version: '16',
      package_manager: 'npm',
      build_tool: 'maven',
      jdk_version: '11'
    }
  },
  server_ip: '',
  server_port: '22',
  server_username: '',
  server_password: '',
  deploy_path: '',
  post_deploy_shell: '',
  max_artifact_days: 0,
  max_artifact_count: 0
}

const newProject = ref({ ...initialProjectForm })
const editingProject = ref({ ...initialProjectForm })

const showCreateDialog = () => {
  newProject.value = { ...initialProjectForm }
  createDialogVisible.value = true
}

const editProject = (project) => {
  editingProject.value = { ...initialProjectForm, ...project }
  editDialogVisible.value = true
}

// 设置筛选器
const setFilter = (filterType) => {
  activeFilter.value = filterType

  // 重置筛选条件
  currentFilters.value = {}

  switch (filterType) {
    case 'robobus':
      currentFilters.value.name_filter = 'robobus'
      break
    case 'ccapi':
      currentFilters.value.name_filter = 'ccapi'
      break
    case 'ccserver':
      currentFilters.value.name_filter = 'ccserver'
      break
    case 'prod':
      currentFilters.value.environment = 'prod'
      break
    case 'test':
      currentFilters.value.environment = 'test'
      break
    case 'all':
    default:
      // 不设置任何筛选条件
      break
  }

  // 重置到第一页并重新获取数据
  pagination.value.currentPage = 1
  fetchProjects()
}

const fetchProjects = async () => {
  try {
    const result = await getProjects(pagination.value.currentPage, pagination.value.pageSize, currentFilters.value)
    projects.value = result.data.items
    pagination.value.total = result.data.total
  } catch (error) {
    console.log('API调用失败，使用模拟数据:', error.message)

    // 模拟数据（移除不必要的延迟）
    const mockData = [
          {
            id: 1,
            name: '测试主机集成项目',
            description: '测试主机集成功能',
            environment: 'test',
            project_type: 'backend',
            git_url: 'https://github.com/test/project.git',
            deploy_type: 'remote',
            status: 'online'
          },
          {
            id: 2,
            name: 'ccapi_prod',
            description: '运营平台生产环境',
            environment: 'prod',
            project_type: 'frontend',
            git_url: 'https://jxd.pixmoving.city:9876/pixcc/ccapi.git',
            deploy_type: 'local',
            status: 'online'
          },
          {
            id: 3,
            name: 'ccapi_test',
            description: '运营平台测试环境',
            environment: 'test',
            project_type: 'backend',
            git_url: 'https://jxd.pixmoving.city:9876/pixcc/ccapi.git',
            deploy_type: 'remote',
            status: 'warning'
          },
          {
            id: 4,
            name: 'ccserver_prod',
            description: 'tcc服务器生产环境',
            environment: 'prod',
            project_type: 'backend',
            git_url: 'https://jxd.pixmoving.city:9876/pixcc/ccserver.git',
            deploy_type: 'remote',
            status: 'online'
          },
          {
            id: 5,
            name: 'ccserver_test',
            description: 'tcc服务器测试环境',
            environment: 'test',
            project_type: 'backend',
            git_url: 'https://jxd.pixmoving.city:9876/pixcc/ccserver.git',
            deploy_type: 'remote',
            status: 'online'
          },
          {
            id: 6,
            name: 'gzy_test',
            description: '工作流测试环境',
            environment: 'test',
            project_type: 'backend',
            git_url: 'https://jxd.pixmoving.city:9876/gzy/gzy.git',
            deploy_type: 'remote',
            status: 'online'
          },
          {
            id: 7,
            name: 'robobus_wx_prod',
            description: '小程序生产环境',
            environment: 'prod',
            project_type: 'frontend',
            git_url: 'https://jxd.pixmoving.city:9876/robobus/robobus_wx.git',
            deploy_type: 'remote',
            status: 'online'
          },
          {
            id: 8,
            name: 'robobus_wx_test',
            description: '小程序测试环境',
            environment: 'test',
            project_type: 'frontend',
            git_url: 'https://jxd.pixmoving.city:9876/robobus/robobus_wx.git',
            deploy_type: 'remote',
            status: 'online'
          },
          {
            id: 9,
            name: 'vehsim',
            description: '车辆模拟器',
            environment: 'prod',
            project_type: 'backend',
            git_url: 'https://jxd.pixmoving.city:9876/dingjie/vehsim.git',
            deploy_type: 'remote',
            status: 'online'
          }
    ]

    projects.value = mockData
    pagination.value.total = mockData.length
    showMessage('获取项目列表失败，显示模拟数据：' + error.message, 'error')
  }
}

const createProject = async (formData) => {
  try {
    await apiCreateProject(formData)
    showMessage('创建项目成功', 'success')
    createDialogVisible.value = false
    await fetchProjects()
  } catch (error) {
    showMessage('创建项目失败：' + error.message, 'error')
  }
}

const updateProject = async (formData) => {
  try {
    await apiUpdateProject(formData)
    showMessage('更新项目成功', 'success')
    editDialogVisible.value = false
    await fetchProjects()
  } catch (error) {
    showMessage('更新项目失败：' + error.message, 'error')
  }
}

const deleteProject = async (project) => {
  try {
    // 🎭 使用精美确认对话框
    await confirm.confirmDelete(project.name, '项目')

    await apiDeleteProject(project.id)
    showMessage('删除项目成功', 'success')
    await fetchProjects()
  } catch (error) {
    // 用户取消删除时不显示错误
    if (error !== false) {
      showMessage('删除项目失败：' + error.message, 'error')
    }
  }
}

const handleCurrentChange = (val) => {
  pagination.value.currentPage = val
  fetchProjects()
}

const handleSizeChange = (val) => {
  pagination.value.pageSize = val
  pagination.value.currentPage = 1
  fetchProjects()
}

// 分页函数已移至Pagination组件中

// 构建项目
const buildProject = async (project) => {
  try {
    currentProjectId.value = project.id
    connectWebSocket(project.id)
    logDialogVisible.value = true
    await apiBuildProject(project.id)
    showMessage('构建请求已发送', 'success')
  } catch (error) {
    showMessage('构建项目失败：' + error.message, 'error')
  }
}

// 一键部署
const oneClickDeploy = async (project) => {
  try {
    currentProjectId.value = project.id
    connectWebSocket(project.id)
    logDialogVisible.value = true
    await apiBuildAndDeployProject(project.id)
    showMessage('部署请求已发送', 'success')
  } catch (error) {
    showMessage('部署项目失败：' + error.message, 'error')
  }
}

// 展示产物弹窗
const showArtifactDialog = async (project) => {
  currentProjectId.value = project.id
  artifactDialogVisible.value = true
  try {
    const res = await getArtifactList(project.id)
    if (res.success) {
      artifactList.value = res.items || res.data?.items || []
    } else {
      artifactList.value = []
      showMessage(res.message || '获取历史产物失败', 'error')
    }
  } catch (e) {
    artifactList.value = []
    showMessage('请求失败', 'error')
  }
}

// 部署产物
const deployArtifact = async (artifact) => {
  try {
    logDialogVisible.value = true
    currentProjectId.value = artifact.project_id
    connectWebSocket(artifact.project_id)
    artifact.deployLoading = true
    await apiDeployArtifact(artifact.id)
    showMessage('部署请求已发送', 'success')
  } catch (e) {
    showMessage('部署失败', 'error')
  }
  artifact.deployLoading = false
}

// 删除产物
const deleteArtifact = async (artifact) => {
  try {
    await apiDeleteArtifact(artifact.id)
    artifactList.value = artifactList.value.filter(item => item.id !== artifact.id)
    showMessage('已删除: ' + artifact.artifact_name, 'success')
  } catch (e) {
    showMessage('删除失败', 'error')
  }
}

// 日志相关
const connectWebSocket = (projectId) => {
  disconnectWebSocket()
  deployLogs.value = []
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
  const wsUrl = `${protocol}//${window.location.host}/ws/project/${projectId}/deploy/logs`
  ws = new WebSocket(wsUrl)
  ws.onmessage = (event) => {
    deployLogs.value.push(event.data)
  }
  ws.onerror = (error) => {
    showMessage('WebSocket连接错误', 'error')
  }
  ws.onclose = () => {
    // 可选：showMessage('WebSocket连接已关闭', 'info')
  }
}
const disconnectWebSocket = () => {
  if (ws) {
    ws.close()
    ws = null
  }
}
const clearLogs = () => {
  deployLogs.value = []
}
const refreshLogs = () => {
  if (currentProjectId.value) {
    disconnectWebSocket()
    connectWebSocket(currentProjectId.value)
  }
}
const downloadLogs = () => {
  // 简单实现：将日志内容导出为txt
  const blob = new Blob([deployLogs.value.join('\n')], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'deploy-logs.txt'
  a.click()
  URL.revokeObjectURL(url)
}
const closeLogPanel = () => {
  logDialogVisible.value = false
  disconnectWebSocket()
}

// 下载产物
const downloadArtifact = (artifact) => {
  const link = document.createElement('a')
  link.href = `/api/artifacts/${artifact.id}/download`
  link.download = artifact.artifact_name
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  showMessage('开始下载: ' + artifact.artifact_name, 'success')
}

// 星标产物
const toggleStar = async (artifact) => {
  try {
    const res = await toggleArtifactStar(artifact.id)
    if (res.success) {
      artifact.is_starred = res.is_starred
      showMessage(res.message, 'success')
    } else if (res.conflict) {
      // 显示冲突警告对话框
      showStarConflictDialog(res.message, res.suggestions)
    } else {
      showMessage(res.message || '星标操作失败', 'error')
    }
  } catch (error) {
    showMessage('星标操作失败：' + error.message, 'error')
  }
}

// 显示星标冲突警告对话框
const showStarConflictDialog = (message, suggestions) => {
  const suggestionList = suggestions.map((s, i) => `${i + 1}. ${s}`).join('\n')

  // 临时使用alert替代ElMessageBox
  alert(`星标冲突警告\n\n${message}\n\n建议解决方案：\n${suggestionList}`)
}

// 查看日志（历史）
const viewLogs = (project) => {
  logDialogVisible.value = true
  // 这里可以考虑请求历史日志接口（如有）
}

onMounted(() => {
  fetchProjects()
})

onUnmounted(() => {
  disconnectWebSocket()
})
</script>

<style scoped>
/* 🌸 浪漫风格项目管理页面样式 */

/* 项目管理页面现在使用全局的 dashboard-container 类，无需额外样式 */

/* 页面头部布局样式 */
.page-header {
  position: relative;
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1.5rem;
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(to right, #ec4899, #9333ea);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.page-description {
  font-size: 1rem;
  color: #6b7280;
  margin: 0;
}

.header-actions {
  flex-shrink: 0;
}

.btn-add {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #ec4899, #a855f7);
  color: white;
  border: none;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(236, 72, 153, 0.1), 0 2px 4px -1px rgba(236, 72, 153, 0.06);
}

.btn-add:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(236, 72, 153, 0.2), 0 4px 6px -2px rgba(236, 72, 153, 0.1);
}

/* 响应式头部布局 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .page-title {
    font-size: 1.75rem;
  }

  .btn-add {
    justify-content: center;
  }
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(236, 72, 153, 0.05), rgba(168, 85, 247, 0.05));
  border-radius: 1rem;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.page-header:hover::before {
  opacity: 1;
}

/* 视图切换按钮组 */
.btn-group {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-group .btn {
  border-radius: 0;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-group .btn:last-child {
  border-right: none;
}

/* 项目卡片网格动画 */
.grid > * {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
  opacity: 0;
}

.grid > *:nth-child(1) { animation-delay: 0.1s; }
.grid > *:nth-child(2) { animation-delay: 0.15s; }
.grid > *:nth-child(3) { animation-delay: 0.2s; }
.grid > *:nth-child(4) { animation-delay: 0.25s; }
.grid > *:nth-child(5) { animation-delay: 0.3s; }
.grid > *:nth-child(6) { animation-delay: 0.35s; }
.grid > *:nth-child(7) { animation-delay: 0.4s; }
.grid > *:nth-child(8) { animation-delay: 0.45s; }
.grid > *:nth-child(9) { animation-delay: 0.5s; }
.grid > *:nth-child(10) { animation-delay: 0.55s; }
.grid > *:nth-child(11) { animation-delay: 0.6s; }
.grid > *:nth-child(12) { animation-delay: 0.65s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 分页组件样式增强 */
.join .btn {
  transition: all 0.3s ease;
}

.join .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(236, 72, 153, 0.3);
}

.join .btn.btn-romantic {
  background: linear-gradient(135deg, #ec4899, #a855f7);
  border-color: transparent;
  color: white;
}







/* 表格淡入动画 */
.table-fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 项目卡片动画 - 类似仪表板效果 */
.project-grid-animated > * {
  animation: slideUp 0.6s ease-out;
  animation-fill-mode: both;
}

.project-grid-animated > *:nth-child(1) { animation-delay: 0.1s; }
.project-grid-animated > *:nth-child(2) { animation-delay: 0.2s; }
.project-grid-animated > *:nth-child(3) { animation-delay: 0.3s; }
.project-grid-animated > *:nth-child(4) { animation-delay: 0.4s; }
.project-grid-animated > *:nth-child(5) { animation-delay: 0.5s; }
.project-grid-animated > *:nth-child(6) { animation-delay: 0.6s; }
.project-grid-animated > *:nth-child(7) { animation-delay: 0.7s; }
.project-grid-animated > *:nth-child(8) { animation-delay: 0.8s; }
.project-grid-animated > *:nth-child(9) { animation-delay: 0.9s; }
.project-grid-animated > *:nth-child(10) { animation-delay: 1.0s; }
.project-grid-animated > *:nth-child(11) { animation-delay: 1.1s; }
.project-grid-animated > *:nth-child(12) { animation-delay: 1.2s; }

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表格淡入动画优化 */
.table-fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* 分页组件淡入动画 */
.pagination-fade-in {
  animation: fadeIn 0.6s ease-out 0.3s both;
}



/* 响应式优化 */
@media (max-width: 640px) {
  .page-header .flex {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .page-header h1 {
    font-size: 1.875rem;
  }

  .btn-group {
    width: 100%;
  }

  .btn-group .btn {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }

  .flex.items-center.justify-between {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .flex.items-center.space-x-4 {
    justify-content: space-between;
    margin: 0;
  }
}

@media (max-width: 1024px) {
  .grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1280px) {
  .grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1536px) {
  .grid {
    grid-template-columns: repeat(5, 1fr);
  }
}
</style>