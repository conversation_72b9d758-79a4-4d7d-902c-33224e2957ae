package services

import (
	"GoShip/models"
	"bytes"
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

// logWithTimeout 带超时的日志记录辅助函数
func logWithTimeout(projectID int64, logType, level, message string, timeout time.Duration) {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	done := make(chan struct{})
	go func() {
		SendDeployLog(projectID, logType, level, message)
		close(done)
	}()

	select {
	case <-done:
		// 日志发送成功
	case <-ctx.Done():
		// 日志发送超时，但不影响主流程
	}
}

// Go语言部署策略
type GoBuildStrategy struct {
	Project *models.Project
}

func (g *GoBuildStrategy) Build(workDir string, buildNumber int) error {
	config := g.Project.BuildConfig.Config
	serverOS, _ := config["server_os"].(string)
	serverArch, _ := config["server_arch"].(string)

	// 1. 拉取代码
	SendDeployLog(g.Project.ID, "git", "start", "开始拉取代码...")
	gitSvc := NewGitService(g.Project.GitURL, g.Project.GitUsername, g.Project.GitPassword)
	gitSvc.GitVersion = g.Project.GitVersion
	if err := gitSvc.CloneOrPull(workDir); err != nil {
		SendDeployLog(g.Project.ID, "git", "error", fmt.Sprintf("拉取代码失败: %v", err))
		return err
	}
	SendDeployLog(g.Project.ID, "git", "success", "代码拉取完成")

	// 构建前输出 commit id 和 message
	commitIDCmd := exec.Command("git", "rev-parse", "HEAD")
	commitIDCmd.Dir = workDir
	commitIDOut, _ := commitIDCmd.Output()
	commitID := strings.TrimSpace(string(commitIDOut))
	SendDeployLog(g.Project.ID, "build", "info", "当前 commit id: "+commitID)

	commitMsgCmd := exec.Command("git", "log", "-1", "--oneline")
	commitMsgCmd.Dir = workDir
	commitMsgOut, _ := commitMsgCmd.Output()
	commitMsg := strings.TrimSpace(string(commitMsgOut))
	SendDeployLog(g.Project.ID, "build", "info", "当前 commit: "+commitMsg)

	// 产物命名递增（基于当天最大序号）
	now := time.Now()
	artifactsToday, err := models.GetArtifactsByProjectIDAndDay(g.Project.ID, now)
	if err != nil {
		SendDeployLog(g.Project.ID, "build", "error", fmt.Sprintf("查询当天产物失败: %v", err))
		return err
	}

	// 找出当天产物的最大序号
	maxSeq := 0
	datePrefix := fmt.Sprintf("%s_%s_", g.Project.Name, now.Format("2006_01_02"))
	for _, artifact := range artifactsToday {
		if strings.HasPrefix(artifact.ArtifactName, datePrefix) {
			// 提取序号部分，例如从 "ccapi_test_2025_07_16_03" 提取 "03"
			seqStr := strings.TrimPrefix(artifact.ArtifactName, datePrefix)
			if seq, err := strconv.Atoi(seqStr); err == nil && seq > maxSeq {
				maxSeq = seq
			}
		}
	}

	// 新序号 = 最大序号 + 1
	newSeq := maxSeq + 1
	outputFile := fmt.Sprintf("%s_%s_%02d", g.Project.Name, now.Format("2006_01_02"), newSeq)
	buildCmd := fmt.Sprintf("GOOS=%s GOARCH=%s go build -o %s", serverOS, serverArch, outputFile)

	cmd := exec.Command("sh", "-c", buildCmd)
	cmd.Dir = workDir

	var output bytes.Buffer
	cmd.Stdout = &output
	cmd.Stderr = &output

	SendDeployLog(g.Project.ID, "build", "info", fmt.Sprintf("执行构建命令: %s", buildCmd))

	if err := cmd.Run(); err != nil {
		if output.Len() > 0 {
			SendDeployLog(g.Project.ID, "build", "error", output.String())
		}
		return fmt.Errorf("Go项目构建失败: %v", err)
	}

	if output.Len() > 0 {
		SendDeployLog(g.Project.ID, "build", "info", output.String())
	}

	SendDeployLog(g.Project.ID, "build", "info", "构建成功，开始处理产物...")

	// === 产物保存与数据库写入 ===
	artifactDir := filepath.Join(models.ArtifactBasePath, g.Project.Name)
	if err := os.MkdirAll(artifactDir, 0755); err != nil {
		SendDeployLog(g.Project.ID, "artifact", "error", fmt.Sprintf("创建产物目录失败: %v", err))
		return err
	}
	artifactPath := filepath.Join(artifactDir, outputFile)
	// 移动产物到artifact目录
	if err := os.Rename(filepath.Join(workDir, outputFile), artifactPath); err != nil {
		SendDeployLog(g.Project.ID, "artifact", "error", fmt.Sprintf("移动产物失败: %v", err))
		return err
	}
	// 获取git_ref（分支或标签）
	gitRef := ""
	if g.Project.GitVersion != "" {
		gitRef = g.Project.GitVersion
	}
	// 获取文件大小
	var size int64 = 0
	if fi, err := os.Stat(artifactPath); err == nil {
		size = fi.Size()
	}
	// === 自动清理逻辑（在保存新产物之前执行） ===
	maxDays := g.Project.MaxArtifactDays
	maxCount := g.Project.MaxArtifactCount
	if maxDays > 0 || maxCount > 0 {
		SendDeployLog(g.Project.ID, "artifact", "info", "开始清理旧产物...")

		// 检查是否存在构建冲突
		if maxCount > 0 {
			conflict, err := models.CheckBuildConflict(g.Project.ID)
			if err != nil {
				SendDeployLog(g.Project.ID, "artifact", "error", fmt.Sprintf("检查构建冲突失败: %v", err))
				return fmt.Errorf("检查构建冲突失败: %v", err)
			}

			if conflict.HasConflict {
				SendDeployLog(g.Project.ID, "artifact", "error", conflict.Message)
				for _, suggestion := range conflict.Suggestions {
					SendDeployLog(g.Project.ID, "artifact", "info", fmt.Sprintf("建议: %s", suggestion))
				}
				return fmt.Errorf("构建冲突: %s", conflict.Message)
			}
		}

		artifacts, err := models.GetNonStarredArtifacts(g.Project.ID)
		if err == nil && len(artifacts) > 0 {
			var deletedCount int

			// 1. 按天数清理
			if maxDays > 0 {
				cutoff := now.AddDate(0, 0, -maxDays)
				for _, a := range artifacts {
					if a.CreatedAt.Before(cutoff) {
						if err := os.Remove(a.ArtifactPath); err == nil {
							if err := models.DeleteArtifactByID(a.ID); err == nil {
								deletedCount++
								SendDeployLog(g.Project.ID, "artifact", "info", fmt.Sprintf("删除过期产物: %s", a.ArtifactName))
							}
						}
					}
				}
			}

			// 2. 按数量清理（需要为新产物预留1个位置）
			if maxCount > 0 {
				// 重新获取未被天数清理后的非星标产物
				artifacts2, _ := models.GetNonStarredArtifacts(g.Project.ID)
				// 如果当前产物数量 >= maxCount，需要删除最旧的产物为新产物腾出空间
				if len(artifacts2) >= maxCount {
					deleteCount := len(artifacts2) - maxCount + 1 // +1 为新产物预留空间
					for i := 0; i < deleteCount && i < len(artifacts2); i++ {
						if err := os.Remove(artifacts2[i].ArtifactPath); err == nil {
							if err := models.DeleteArtifactByID(artifacts2[i].ID); err == nil {
								deletedCount++
								SendDeployLog(g.Project.ID, "artifact", "info", fmt.Sprintf("删除旧产物: %s", artifacts2[i].ArtifactName))
							}
						}
					}
				}
			}

			if deletedCount > 0 {
				SendDeployLog(g.Project.ID, "artifact", "info", fmt.Sprintf("清理完成，删除了 %d 个旧产物", deletedCount))
			}
		}
	}

	artifact := &models.Artifact{
		ProjectID:    g.Project.ID,
		ProjectName:  g.Project.Name,
		ArtifactName: outputFile,
		ArtifactPath: artifactPath,
		GitRef:       gitRef,
		GitCommitID:  &commitID,
		GitCommitMsg: &commitMsg,
		CreatedAt:    now,
		Size:         size,
		IsStarred:    false,
		Remark:       "",
	}
	if err := models.InsertArtifact(artifact); err != nil {
		SendDeployLog(g.Project.ID, "artifact", "error", fmt.Sprintf("写入产物元数据失败: %v", err))
		return err
	}
	SendDeployLog(g.Project.ID, "artifact", "success", fmt.Sprintf("产物已保存: %s", artifactPath))

	return nil
}

func (g *GoBuildStrategy) Deploy(workDir string, buildNumber int) error {
	if g.Project.DeployType == "remote" {
		return g.deployToRemote(workDir, buildNumber)
	} else {
		return g.deployToLocal(workDir)
	}
}

// 本地部署
func (g *GoBuildStrategy) deployToLocal(workDir string) error {
	if err := os.MkdirAll(g.Project.DeployPath, 0755); err != nil {
		SendDeployLog(g.Project.ID, "deploy", "error", fmt.Sprintf("创建部署目录失败: %v", err))
		return err
	}
	cmd := exec.Command("cp", "-r", filepath.Join(workDir, "*"), g.Project.DeployPath)
	var output bytes.Buffer
	cmd.Stdout = &output
	cmd.Stderr = &output
	if err := cmd.Run(); err != nil {
		SendDeployLog(g.Project.ID, "deploy", "error", fmt.Sprintf("复制文件失败: %v", err))
		return err
	}
	if output.Len() > 0 {
		SendDeployLog(g.Project.ID, "deploy", "info", output.String())
	}
	return nil
}

// 远程部署
func (g *GoBuildStrategy) deployToRemote(workDir string, buildNumber int) error {
	// 获取项目关联的主机配置
	host, err := g.Project.GetProjectHost()
	if err != nil {
		SendDeployLog(g.Project.ID, "deploy", "error", fmt.Sprintf("获取主机配置失败: %v", err))
		return err
	}

	SendDeployLog(g.Project.ID, "deploy", "info", fmt.Sprintf("使用主机: %s (%s:%d)", host.Name, host.IP, host.Port))

	// 检查 sshpass 工具
	SendDeployLog(g.Project.ID, "deploy", "info", "检查 sshpass 工具...")
	if _, err := exec.LookPath("sshpass"); err != nil {
		SendDeployLog(g.Project.ID, "deploy", "error", "未找到 sshpass 工具，请先安装: brew install hudochenkov/sshpass/sshpass 或 apt install sshpass")
		return fmt.Errorf("sshpass 工具未安装")
	}

	// 准备产物文件
	now := time.Now()
	outputFile := fmt.Sprintf("%s_%s_%02d", g.Project.Name, now.Format("2006_01_02"), buildNumber)
	artifactDir := filepath.Join(models.ArtifactBasePath, g.Project.Name)
	localFilePath := filepath.Join(artifactDir, outputFile)

	// 检查本地文件是否存在
	if _, err := os.Stat(localFilePath); os.IsNotExist(err) {
		SendDeployLog(g.Project.ID, "deploy", "error", fmt.Sprintf("本地产物文件不存在: %s", localFilePath))
		return err
	}

	// 获取文件大小
	fileInfo, err := os.Stat(localFilePath)
	if err != nil {
		SendDeployLog(g.Project.ID, "deploy", "error", fmt.Sprintf("获取文件信息失败: %v", err))
		return err
	}
	fileSizeMB := float64(fileInfo.Size()) / 1024 / 1024
	SendDeployLog(g.Project.ID, "deploy", "info", fmt.Sprintf("本地文件大小: %.2f MB", fileSizeMB))

	// 检查并创建远程目录
	SendDeployLog(g.Project.ID, "deploy", "info", "检查并创建远程目录...")
	createDirCmd := fmt.Sprintf("mkdir -p %s", g.Project.DeployPath)
	if err := g.executeSSHCommand(host, createDirCmd); err != nil {
		SendDeployLog(g.Project.ID, "deploy", "error", fmt.Sprintf("创建远程目录失败: %v", err))
		return err
	}
	SendDeployLog(g.Project.ID, "deploy", "success", "远程目录检查完成")

	// 使用 scp 上传文件为 .new 文件（原子替换第一步）
	remoteNewPath := fmt.Sprintf("%s/%s.new", g.Project.DeployPath, outputFile)
	SendDeployLog(g.Project.ID, "deploy", "info", fmt.Sprintf("开始上传文件到: %s", remoteNewPath))

	if err := g.uploadFileWithSCP(host, localFilePath, remoteNewPath); err != nil {
		SendDeployLog(g.Project.ID, "deploy", "error", fmt.Sprintf("文件上传失败: %v", err))
		return err
	}
	SendDeployLog(g.Project.ID, "deploy", "success", "文件上传完成")

	// 验证上传文件完整性
	SendDeployLog(g.Project.ID, "deploy", "info", "验证上传文件完整性...")
	checkCmd := fmt.Sprintf("ls -la %s", remoteNewPath)
	if err := g.executeSSHCommand(host, checkCmd); err != nil {
		SendDeployLog(g.Project.ID, "deploy", "error", fmt.Sprintf("验证上传文件失败: %v", err))
		return err
	}
	SendDeployLog(g.Project.ID, "deploy", "success", "文件完整性验证通过")

	// 停止可能占用文件的进程
	SendDeployLog(g.Project.ID, "deploy", "info", "检查并停止占用文件的进程...")
	stopProcessCmd := fmt.Sprintf(`cd %s &&
		# 尝试停止可能占用文件的进程
		if [ -f "%s" ]; then
			# 查找可能占用该文件的进程
			PIDS=$(lsof +D . 2>/dev/null | grep "%s" | awk '{print $2}' | sort -u)
			if [ -n "$PIDS" ]; then
				echo "发现占用文件的进程: $PIDS"
				for pid in $PIDS; do
					kill -TERM $pid 2>/dev/null || true
				done
				sleep 2
				# 强制杀死仍然存在的进程
				for pid in $PIDS; do
					kill -9 $pid 2>/dev/null || true
				done
				sleep 1
			fi
		fi`, g.Project.DeployPath, outputFile, outputFile)

	if err := g.executeSSHCommand(host, stopProcessCmd); err != nil {
		SendDeployLog(g.Project.ID, "deploy", "warning", fmt.Sprintf("停止进程命令执行失败: %v", err))
		// 不返回错误，继续执行
	}

	// 执行原子替换操作
	SendDeployLog(g.Project.ID, "deploy", "info", "执行原子替换操作...")
	replaceCmd := fmt.Sprintf("cd %s && mv '%s.new' '%s' && chmod +x '%s'",
		g.Project.DeployPath, outputFile, outputFile, outputFile)

	if err := g.executeSSHCommand(host, replaceCmd); err != nil {
		SendDeployLog(g.Project.ID, "deploy", "error", fmt.Sprintf("原子替换失败: %v", err))
		return err
	}
	SendDeployLog(g.Project.ID, "deploy", "success", "原子替换完成")

	// 等待文件句柄完全释放
	SendDeployLog(g.Project.ID, "deploy", "info", "等待文件句柄释放...")
	time.Sleep(3 * time.Second)

	// 验证最终文件
	SendDeployLog(g.Project.ID, "deploy", "info", "验证部署文件...")
	verifyCmd := fmt.Sprintf("ls -la %s/%s", g.Project.DeployPath, outputFile)
	if err := g.executeSSHCommand(host, verifyCmd); err != nil {
		SendDeployLog(g.Project.ID, "deploy", "warning", fmt.Sprintf("验证最终文件失败: %v", err))
	} else {
		SendDeployLog(g.Project.ID, "deploy", "success", "部署文件验证成功")
	}

	// 执行部署后脚本
	if g.Project.PostDeployShell != "" {
		SendDeployLog(g.Project.ID, "deploy", "info", "准备执行部署后脚本...")
		if err := g.executePostDeployScript(host, outputFile); err != nil {
			SendDeployLog(g.Project.ID, "deploy", "warning", fmt.Sprintf("部署后脚本执行失败: %v", err))
			SendDeployLog(g.Project.ID, "deploy", "warning", "文件部署成功，但部署后脚本执行失败")
		} else {
			SendDeployLog(g.Project.ID, "deploy", "success", "部署后脚本执行成功")
		}
	}

	SendDeployLog(g.Project.ID, "deploy", "success", "远程部署完成")
	return nil
}

// uploadFileWithSCP 使用 scp 上传文件
func (g *GoBuildStrategy) uploadFileWithSCP(host *models.Host, localPath, remotePath string) error {
	// 构建 scp 命令
	scpCmd := []string{
		"sshpass", "-p", host.Password,
		"scp", "-P", fmt.Sprintf("%d", host.Port),
		"-o", "StrictHostKeyChecking=no",
		"-o", "UserKnownHostsFile=/dev/null",
		"-o", "LogLevel=ERROR",
		localPath,
		fmt.Sprintf("%s@%s:%s", host.Username, host.IP, remotePath),
	}

	cmd := exec.Command(scpCmd[0], scpCmd[1:]...)
	var output bytes.Buffer
	cmd.Stdout = &output
	cmd.Stderr = &output

	// 执行命令
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("scp 命令执行失败: %v, 输出: %s", err, output.String())
	}

	if output.Len() > 0 {
		SendDeployLog(g.Project.ID, "deploy", "info", fmt.Sprintf("scp 输出: %s", output.String()))
	}

	return nil
}

// executeSSHCommand 使用 ssh 执行远程命令
func (g *GoBuildStrategy) executeSSHCommand(host *models.Host, command string) error {
	// 构建 ssh 命令
	sshCmd := []string{
		"sshpass", "-p", host.Password,
		"ssh", "-p", fmt.Sprintf("%d", host.Port),
		"-o", "StrictHostKeyChecking=no",
		"-o", "UserKnownHostsFile=/dev/null",
		"-o", "LogLevel=ERROR",
		fmt.Sprintf("%s@%s", host.Username, host.IP),
		command,
	}

	cmd := exec.Command(sshCmd[0], sshCmd[1:]...)
	var output bytes.Buffer
	cmd.Stdout = &output
	cmd.Stderr = &output

	// 执行命令
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("ssh 命令执行失败: %v, 输出: %s", err, output.String())
	}

	if output.Len() > 0 {
		SendDeployLog(g.Project.ID, "deploy", "info", fmt.Sprintf("命令输出: %s", output.String()))
	}

	return nil
}

// executePostDeployScript 执行部署后脚本（带重试机制）
func (g *GoBuildStrategy) executePostDeployScript(host *models.Host, artifactName string) error {
	// 构建部署后脚本命令
	command := fmt.Sprintf("cd %s && export BUILD_ARTIFACT='%s' && %s",
		g.Project.DeployPath, artifactName, g.Project.PostDeployShell)

	// 重试机制处理 "Text file busy" 错误
	maxRetries := 3
	retryDelay := 2 * time.Second

	for attempt := 1; attempt <= maxRetries; attempt++ {
		if attempt > 1 {
			SendDeployLog(g.Project.ID, "post_deploy", "info", fmt.Sprintf("第 %d 次重试执行部署后脚本...", attempt))
			time.Sleep(retryDelay)
		}

		// 构建 ssh 命令
		sshCmd := []string{
			"sshpass", "-p", host.Password,
			"ssh", "-p", fmt.Sprintf("%d", host.Port),
			"-o", "StrictHostKeyChecking=no",
			"-o", "UserKnownHostsFile=/dev/null",
			"-o", "LogLevel=ERROR",
			fmt.Sprintf("%s@%s", host.Username, host.IP),
			command,
		}

		cmd := exec.Command(sshCmd[0], sshCmd[1:]...)
		var output bytes.Buffer
		cmd.Stdout = &output
		cmd.Stderr = &output

		// 执行命令
		err := cmd.Run()
		if err == nil {
			// 成功执行
			if output.Len() > 0 {
				SendDeployLog(g.Project.ID, "post_deploy", "info", fmt.Sprintf("脚本输出: %s", output.String()))
			}
			SendDeployLog(g.Project.ID, "post_deploy", "success", "部署后脚本执行完成")
			return nil
		}

		// 检查是否是 "Text file busy" 错误
		errOutput := output.String()
		if strings.Contains(errOutput, "Text file busy") || strings.Contains(errOutput, "text file busy") {
			if attempt < maxRetries {
				SendDeployLog(g.Project.ID, "post_deploy", "warning",
					fmt.Sprintf("检测到 'Text file busy' 错误，%d秒后重试 (第%d/%d次)",
						int(retryDelay.Seconds()), attempt, maxRetries))
				continue
			} else {
				SendDeployLog(g.Project.ID, "post_deploy", "error",
					"多次重试后仍然出现 'Text file busy' 错误，建议手动检查进程状态")
			}
		}

		// 其他错误或最后一次重试失败
		if attempt == maxRetries {
			return fmt.Errorf("部署后脚本执行失败: %v, 输出: %s", err, errOutput)
		}
	}

	return nil
}

// GoBuildStrategy 支持产物部署
func (g *GoBuildStrategy) DeployArtifact(artifact *models.Artifact) error {
	if g.Project.DeployType == "remote" {
		// 获取项目关联的主机配置
		SendDeployLog(g.Project.ID, "deploy", "info", "正在获取主机配置...")
		host, err := g.Project.GetProjectHost()
		if err != nil {
			SendDeployLog(g.Project.ID, "deploy", "error", fmt.Sprintf("获取主机配置失败: %v", err))
			return err
		}

		SendDeployLog(g.Project.ID, "deploy", "info", fmt.Sprintf("使用主机: %s (%s:%d)", host.Name, host.IP, host.Port))
		SendDeployLog(g.Project.ID, "deploy", "info", fmt.Sprintf("认证方式: %s", host.AuthType))

		// 网络连通性检查
		SendDeployLog(g.Project.ID, "deploy", "info", "检查网络连通性...")
		if err := CheckHostConnectivity(host); err != nil {
			SendDeployLog(g.Project.ID, "deploy", "error", fmt.Sprintf("网络连通性检查失败: %v", err))
			SendDeployLog(g.Project.ID, "deploy", "error", "请检查: 1) 主机IP地址是否正确 2) 端口是否开放 3) 网络是否畅通")
			return err
		}
		SendDeployLog(g.Project.ID, "deploy", "success", "网络连通性检查通过")

		// 检查 sshpass 工具
		SendDeployLog(g.Project.ID, "deploy", "info", "检查 sshpass 工具...")
		if _, err := exec.LookPath("sshpass"); err != nil {
			SendDeployLog(g.Project.ID, "deploy", "error", "未找到 sshpass 工具，请先安装: brew install hudochenkov/sshpass/sshpass 或 apt install sshpass")
			return fmt.Errorf("sshpass 工具未安装")
		}

		// 检查本地文件
		SendDeployLog(g.Project.ID, "deploy", "info", fmt.Sprintf("检查本地文件: %s", artifact.ArtifactPath))
		if _, err := os.Stat(artifact.ArtifactPath); os.IsNotExist(err) {
			SendDeployLog(g.Project.ID, "deploy", "error", fmt.Sprintf("本地产物文件不存在: %s", artifact.ArtifactPath))
			return err
		}

		// 获取文件大小
		fileInfo, err := os.Stat(artifact.ArtifactPath)
		if err != nil {
			SendDeployLog(g.Project.ID, "deploy", "error", fmt.Sprintf("获取文件信息失败: %v", err))
			return err
		}
		fileSizeMB := float64(fileInfo.Size()) / 1024 / 1024
		SendDeployLog(g.Project.ID, "deploy", "info", fmt.Sprintf("本地文件大小: %.2f MB", fileSizeMB))

		// 检查并创建远程目录
		SendDeployLog(g.Project.ID, "deploy", "info", "检查并创建远程目录...")
		createDirCmd := fmt.Sprintf("mkdir -p %s", g.Project.DeployPath)
		if err := g.executeSSHCommand(host, createDirCmd); err != nil {
			SendDeployLog(g.Project.ID, "deploy", "error", fmt.Sprintf("创建远程目录失败: %v", err))
			return err
		}
		SendDeployLog(g.Project.ID, "deploy", "success", "远程目录检查完成")

		// 使用 scp 上传文件为 .new 文件（原子替换第一步）
		remoteNewPath := fmt.Sprintf("%s/%s.new", g.Project.DeployPath, artifact.ArtifactName)
		SendDeployLog(g.Project.ID, "deploy", "info", fmt.Sprintf("开始上传文件到: %s", remoteNewPath))

		if err := g.uploadFileWithSCP(host, artifact.ArtifactPath, remoteNewPath); err != nil {
			SendDeployLog(g.Project.ID, "deploy", "error", fmt.Sprintf("文件上传失败: %v", err))
			return err
		}
		SendDeployLog(g.Project.ID, "deploy", "success", "文件上传完成")

		// 验证上传文件完整性
		SendDeployLog(g.Project.ID, "deploy", "info", "验证上传文件完整性...")
		checkCmd := fmt.Sprintf("ls -la %s", remoteNewPath)
		if err := g.executeSSHCommand(host, checkCmd); err != nil {
			SendDeployLog(g.Project.ID, "deploy", "error", fmt.Sprintf("验证上传文件失败: %v", err))
			return err
		}
		SendDeployLog(g.Project.ID, "deploy", "success", "文件完整性验证通过")

		// 停止可能占用文件的进程
		SendDeployLog(g.Project.ID, "deploy", "info", "检查并停止占用文件的进程...")
		stopProcessCmd := fmt.Sprintf(`cd %s &&
			# 尝试停止可能占用文件的进程
			if [ -f "%s" ]; then
				# 查找可能占用该文件的进程
				PIDS=$(lsof +D . 2>/dev/null | grep "%s" | awk '{print $2}' | sort -u)
				if [ -n "$PIDS" ]; then
					echo "发现占用文件的进程: $PIDS"
					for pid in $PIDS; do
						kill -TERM $pid 2>/dev/null || true
					done
					sleep 2
					# 强制杀死仍然存在的进程
					for pid in $PIDS; do
						kill -9 $pid 2>/dev/null || true
					done
					sleep 1
				fi
			fi`, g.Project.DeployPath, artifact.ArtifactName, artifact.ArtifactName)

		if err := g.executeSSHCommand(host, stopProcessCmd); err != nil {
			SendDeployLog(g.Project.ID, "deploy", "warning", fmt.Sprintf("停止进程命令执行失败: %v", err))
			// 不返回错误，继续执行
		}

		// 执行原子替换操作
		SendDeployLog(g.Project.ID, "deploy", "info", "执行原子替换操作...")
		replaceCmd := fmt.Sprintf("cd %s && mv '%s.new' '%s' && chmod +x '%s'",
			g.Project.DeployPath, artifact.ArtifactName, artifact.ArtifactName, artifact.ArtifactName)

		if err := g.executeSSHCommand(host, replaceCmd); err != nil {
			SendDeployLog(g.Project.ID, "deploy", "error", fmt.Sprintf("原子替换失败: %v", err))
			return err
		}
		SendDeployLog(g.Project.ID, "deploy", "success", "原子替换完成")

		// 等待文件句柄完全释放
		SendDeployLog(g.Project.ID, "deploy", "info", "等待文件句柄释放...")
		time.Sleep(3 * time.Second)

		// 验证最终文件
		SendDeployLog(g.Project.ID, "deploy", "info", "验证部署文件...")
		verifyCmd := fmt.Sprintf("ls -la %s/%s", g.Project.DeployPath, artifact.ArtifactName)
		if err := g.executeSSHCommand(host, verifyCmd); err != nil {
			SendDeployLog(g.Project.ID, "deploy", "warning", fmt.Sprintf("验证最终文件失败: %v", err))
		} else {
			SendDeployLog(g.Project.ID, "deploy", "success", "部署文件验证成功")
		}

		// 执行部署后脚本
		if g.Project.PostDeployShell != "" {
			SendDeployLog(g.Project.ID, "deploy", "info", "准备执行部署后脚本...")
			if err := g.executePostDeployScript(host, artifact.ArtifactName); err != nil {
				SendDeployLog(g.Project.ID, "deploy", "warning", fmt.Sprintf("部署后脚本执行失败: %v", err))
				SendDeployLog(g.Project.ID, "deploy", "warning", "文件部署成功，但部署后脚本执行失败")
			} else {
				SendDeployLog(g.Project.ID, "deploy", "success", "部署后脚本执行成功")
			}
		}

		SendDeployLog(g.Project.ID, "deploy", "success", "远程部署完成")
		return nil
	} else {
		// 本地部署产物
		if err := os.MkdirAll(g.Project.DeployPath, 0755); err != nil {
			SendDeployLog(g.Project.ID, "deploy", "error", fmt.Sprintf("创建部署目录失败: %v", err))
			return err
		}
		cmd := exec.Command("cp", artifact.ArtifactPath, g.Project.DeployPath)
		var output bytes.Buffer
		cmd.Stdout = &output
		cmd.Stderr = &output
		if err := cmd.Run(); err != nil {
			SendDeployLog(g.Project.ID, "deploy", "error", fmt.Sprintf("复制产物失败: %v", err))
			return err
		}
		if output.Len() > 0 {
			SendDeployLog(g.Project.ID, "deploy", "info", output.String())
		}
		SendDeployLog(g.Project.ID, "deploy", "success", "产物上传完成")
		// 调用通用部署后脚本
		if err := PostDeploy(g.Project, artifact.ArtifactName); err != nil {
			SendDeployLog(g.Project.ID, "deploy", "warning", fmt.Sprintf("部署后脚本执行失败: %v", err))
			SendDeployLog(g.Project.ID, "deploy", "warning", "文件部署成功，但部署后脚本执行失败")
		} else {
			SendDeployLog(g.Project.ID, "deploy", "success", "部署后脚本执行成功")
		}
		return nil
	}
}
