package services

import (
	"bytes"
	"fmt"
	"os/exec"
)

// UploadAndReplace 通过scp上传为.new文件，上传后ssh原子替换为正式文件名并chmod +x
func UploadAndReplace(remoteUser, remotePass, remoteHost, remotePort, localFile, remoteDir, remoteFileName string) error {
	// 1. 上传为 .new 文件
	remotePath := fmt.Sprintf("%s/%s.new", remoteDir, remoteFileName)
	cmdStr := fmt.Sprintf("LANG=en_US.UTF-8 sshpass -p '%s' scp -P %s -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o LogLevel=ERROR '%s' %s@%s:'%s'", remotePass, remotePort, localFile, remoteUser, remoteHost, remotePath)
	cmd := exec.Command("sh", "-c", cmdStr)
	var scpOutput bytes.Buffer
	cmd.Stdout = &scpOutput
	cmd.Stderr = &scpOutput
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("scp上传失败: %v, 输出: %s", err, scpOutput.String())
	}
	// 2. 原子替换
	replaceCmd := fmt.Sprintf("mv '%s.new' '%s' && chmod +x '%s'", remoteFileName, remoteFileName, remoteFileName)
	sshCmd := fmt.Sprintf("cd %s && %s", remoteDir, replaceCmd)
	cmdReplace := exec.Command("sshpass", "-p", remotePass, "ssh", "-p", remotePort, "-o", "StrictHostKeyChecking=no", "-o", "UserKnownHostsFile=/dev/null", "-o", "LogLevel=ERROR", fmt.Sprintf("%s@%s", remoteUser, remoteHost), sshCmd)
	var replaceOutput bytes.Buffer
	cmdReplace.Stdout = &replaceOutput
	cmdReplace.Stderr = &replaceOutput
	if err := cmdReplace.Run(); err != nil {
		return fmt.Errorf("原子替换失败: %v, 输出: %s", err, replaceOutput.String())
	}
	return nil
}
