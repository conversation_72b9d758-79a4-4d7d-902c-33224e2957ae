package controllers

import (
	"GoShip/services"
	"fmt"
	"net/http"
	"strconv"

	beego "github.com/beego/beego/v2/server/web"
	"github.com/gorilla/websocket"
)

type WebSocketController struct {
	beego.Controller
}

// DeployLogs WebSocket 连接处理
func (c *WebSocketController) DeployLogs() {
	// 禁用模板渲染
	c.EnableRender = false

	// 获取项目ID
	projectID, err := strconv.ParseInt(c.Ctx.Input.Param(":id"), 10, 64)
	if err != nil {
		c.Ctx.Output.SetStatus(400)
		c.Ctx.Output.Body([]byte("无效的项目ID"))
		return
	}

	// 升级 HTTP 连接为 WebSocket
	upgrader := websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}

	ws, err := upgrader.Upgrade(c.Ctx.ResponseWriter, c.Ctx.Request, nil)
	if err != nil {
		fmt.Printf("升级WebSocket连接失败: %v\n", err)
		return
	}
	defer ws.Close()

	// 添加连接到管理器
	services.WSManager.AddConnection(projectID, ws)
	defer services.WSManager.RemoveConnection(projectID, ws)

	// 保持连接活跃
	for {
		_, _, err := ws.ReadMessage()
		if err != nil {
			break
		}
	}
}
